<template>
  <div class="submit-page">
    <div class="container-fluid">
      <el-row :gutter="15" class="mt-1">
        <el-col :span="6">
          <div class="card">
            <h3 class="mt-0">Submit</h3>
            <el-divider class="mt-1"></el-divider>

            <el-form
              :model="form"
              label-width="auto"
              style="max-width: 600px"
              label-position="top"
            >
              <!-- Main Radio Group -->
              <el-form-item>
                <el-radio-group
                  v-model="form.analysisType"
                  @change="handleAnalysisTypeChange"
                >
                  <el-radio value="biogeography">Biogeography</el-radio>
                  <el-radio value="species-diversity"
                    >Species Diversity</el-radio
                  >
                </el-radio-group>
              </el-form-item>

              <!-- Species Section -->
              <el-form-item>
                <template #label>
                  <div class="section-label">Species</div>
                </template>
                <div class="species-section">
                  <!-- Domain Radio Button Group -->
                  <el-form-item label="Domain" class="mb-2">
                    <el-radio-group
                      v-model="form.domain"
                      class="custom-radio-buttons"
                    >
                      <el-radio-button value="archaea">Archaea</el-radio-button>
                      <el-radio-button value="bacteria"
                        >Bacteria</el-radio-button
                      >
                      <el-radio-button value="eukaryota"
                        >Eukaryota</el-radio-button
                      >
                      <el-radio-button value="virus">Virus</el-radio-button>
                    </el-radio-group>
                  </el-form-item>

                  <!-- Taxonomy Radio Button Group -->
                  <el-form-item label="Taxonomy" class="mb-2">
                    <el-radio-group
                      v-model="form.taxonomy"
                      class="custom-radio-buttons"
                    >
                      <el-radio-button value="phylum">Phylum</el-radio-button>
                      <el-radio-button value="class">Class</el-radio-button>
                      <el-radio-button value="order">Order</el-radio-button>
                      <el-radio-button value="family">Family</el-radio-button>
                      <el-radio-button value="genus">Genus</el-radio-button>
                      <el-radio-button value="species">Species</el-radio-button>
                    </el-radio-group>
                  </el-form-item>

                  <!-- Species Name Multi-select -->
                  <el-form-item label="Species Name" class="mb-2">
                    <el-select
                      v-model="form.speciesNames"
                      multiple
                      filterable
                      placeholder="Select species names"
                      class="w-100"
                      :max-collapse-tags="3"
                    >
                      <el-option
                        v-for="it in speciesNameOptions"
                        :key="it.value"
                        :label="it.label"
                        :value="it.value"
                      />
                    </el-select>
                    <div class="species-warning">
                      The maximum number of queried species name is 10
                    </div>
                  </el-form-item>
                </div>
              </el-form-item>

              <!-- Data Filter Section -->
              <el-form-item>
                <template #label>
                  <div class="section-label">Data Filter</div>
                </template>
                <!-- Biogeography Mode -->
                <div
                  v-if="form.analysisType === 'biogeography'"
                  class="data-filter-section"
                >
                  <el-form-item label="Longitude" class="mb-2">
                    <el-slider
                      v-model="form.sliderLongitude"
                      range
                      :max="180.0"
                      :min="-180.0"
                      step="0.01"
                      placement="right"
                    />
                    <div class="d-flex align-items-center justify-center">
                      <el-input
                        v-model="form.longitudeTo"
                        style="width: 130px"
                      />
                      <span class="mr-05 ml-05">~</span>
                      <el-input
                        v-model="form.longitudeFrom"
                        style="width: 130px"
                      />
                    </div>
                  </el-form-item>

                  <el-form-item label="Latitude" class="mb-2">
                    <el-slider
                      v-model="form.sliderLatitude"
                      range
                      :max="90"
                      :min="-90"
                      step="0.01"
                      placement="right"
                    />
                    <div class="d-flex align-items-center justify-center">
                      <el-input
                        v-model="form.latitudeTo"
                        style="width: 130px"
                      />
                      <span class="mr-05 ml-05">~</span>
                      <el-input
                        v-model="form.latitudeFrom"
                        style="width: 130px"
                      />
                    </div>
                  </el-form-item>

                  <el-form-item label="Hydrosphere Type" class="mb-2">
                    <el-select
                      v-model="form.hydrosphereType"
                      placeholder="Select hydrosphere type"
                      class="w-100"
                    >
                      <el-option label="All" value="all" />
                      <el-option label="Inland water" value="inland-water" />
                      <el-option label="Marine" value="marine" />
                    </el-select>
                  </el-form-item>

                  <el-form-item label="Geolocation" class="mb-2">
                    <el-select
                      v-model="form.geolocation"
                      placeholder="Select geolocation"
                      class="w-100"
                    >
                      <el-option
                        v-for="it in geolocationOptions"
                        :key="it.value"
                        :label="it.label"
                        :value="it.value"
                      />
                    </el-select>
                  </el-form-item>

                  <el-form-item class="mb-2">
                    <div class="cart-form-item">
                      <span class="cart-label">Or Select Data From Cart</span>
                      <div class="cart-section">
                        <UnifiedCart
                          variant="inline"
                          @confirm-selection="handleCartSelection"
                        />
                      </div>
                    </div>
                    <!-- 显示选中的数据条数 -->
                    <div
                      v-if="selectedCartCount > 0"
                      class="selected-data-info"
                    >
                      <el-tag type="success">
                        {{ selectedCartCount }} items selected from cart
                      </el-tag>
                    </div>
                  </el-form-item>
                </div>

                <!-- Species Diversity Mode -->
                <div
                  v-else-if="form.analysisType === 'species-diversity'"
                  class="data-filter-section"
                >
                  <div
                    v-for="(group, index) in form.diversityGroups"
                    :key="group.id"
                    class="diversity-group mb-3"
                  >
                    <div
                      class="group-header d-flex justify-space-between align-items-center mb-2"
                    >
                      <el-input
                        v-model="group.name"
                        class="group-title-input"
                        size="small"
                        :style="{ width: '120px' }"
                      />
                      <div class="group-actions">
                        <el-button
                          v-if="index === form.diversityGroups.length - 1"
                          type="success"
                          size="small"
                          round
                          :icon="Plus"
                          @click="addGroup"
                        >
                        </el-button>
                        <el-button
                          v-if="form.diversityGroups.length > 1"
                          type="danger"
                          size="small"
                          round
                          :icon="Minus"
                          @click="removeGroup(index)"
                        >
                        </el-button>
                      </div>
                    </div>

                    <div class="group-content">
                      <el-form-item label="Longitude" class="mb-2">
                        <el-slider
                          v-model="group.sliderLongitude"
                          range
                          :max="180.0"
                          :min="-180.0"
                          step="0.01"
                          placement="right"
                        />
                        <div class="d-flex align-items-center justify-center">
                          <el-input
                            v-model="group.longitudeTo"
                            style="width: 130px"
                          />
                          <span class="mr-05 ml-05">~</span>
                          <el-input
                            v-model="group.longitudeFrom"
                            style="width: 130px"
                          />
                        </div>
                      </el-form-item>

                      <el-form-item label="Latitude" class="mb-2">
                        <el-slider
                          v-model="group.sliderLatitude"
                          range
                          :max="90"
                          :min="-90"
                          step="0.01"
                          placement="right"
                        />
                        <div class="d-flex align-items-center justify-center">
                          <el-input
                            v-model="group.latitudeTo"
                            style="width: 130px"
                          />
                          <span class="mr-05 ml-05">~</span>
                          <el-input
                            v-model="group.latitudeFrom"
                            style="width: 130px"
                          />
                        </div>
                      </el-form-item>

                      <el-form-item label="Hydrosphere Type" class="mb-2">
                        <el-select
                          v-model="group.hydrosphereType"
                          placeholder="Select hydrosphere type"
                          class="w-100"
                        >
                          <el-option label="All" value="all" />
                          <el-option
                            label="Inland water"
                            value="inland-water"
                          />
                          <el-option label="Marine" value="marine" />
                        </el-select>
                      </el-form-item>

                      <el-form-item label="Geolocation" class="mb-2">
                        <el-select
                          v-model="group.geolocation"
                          placeholder="Select geolocation"
                          class="w-100"
                        >
                          <el-option
                            v-for="it in geolocationOptions"
                            :key="it.value"
                            :label="it.label"
                            :value="it.value"
                          />
                        </el-select>
                      </el-form-item>

                      <el-form-item class="mb-2">
                        <div class="cart-form-item">
                          <span class="cart-label">or Use Shopping Cart</span>
                          <div class="cart-section flex-1">
                            <UnifiedCart
                              variant="inline"
                              @confirm-selection="handleCartSelection"
                            />
                          </div>
                        </div>
                        <!-- 显示选中的数据条数 -->
                        <div
                          v-if="selectedCartCount > 0"
                          class="selected-data-info"
                        >
                          <el-tag type="success" size="small">
                            {{ selectedCartCount }} items selected from cart
                          </el-tag>
                        </div>
                      </el-form-item>
                    </div>
                  </div>
                </div>
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  :icon="Promotion"
                  class="w-100 filter-search mt-1"
                  >Submit
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-col>
        <el-col :span="18">
          <div
            v-show="form.analysisType === 'biogeography'"
            class="card mb-1 pos-relative"
          >
            <div class="d-flex justify-space-between align-items-center">
              <h3 class="mb-0 mt-0">
                Global distribution and relativate abundance of K01682
              </h3>
              <div class="d-flex justify-space-between align-items-center">
                <el-select
                  v-model="summarySelect"
                  filterable
                  placeholder="Select species names"
                  class="mr-1"
                  style="width: 300px"
                >
                  <el-option
                    label="K00239; sdhA,frdA; succinate dehydrogenase flavoprotein subunit [EC:1.3.5.1]"
                    value="K00239; sdhA,frdA; succinate dehydrogenase flavoprotein subunit [EC:1.3.5.1]"
                  />
                  <el-option
                    label="K00237; SDHD,SDH4; succinate dehydrogenase (ubiquinone) membrane anchor subunit"
                    value="K00237; SDHD,SDH4; succinate dehydrogenase (ubiquinone) membrane anchor subunit"
                  />
                </el-select>
                <el-button type="primary">Show Summary</el-button>
              </div>
            </div>
            <el-divider class="mt-1"></el-divider>

            <div class="map-container" :class="{ fullscreen: isMapFullscreen }">
              <div
                id="biotaMap"
                style="width: 100%; height: 560px; background-color: #fffff5"
              ></div>
              <!-- 全屏切换按钮 -->
              <el-button
                class="fullscreen-btn"
                type="primary"
                :icon="isMapFullscreen ? 'FullScreen' : 'Rank'"
                circle
                :title="isMapFullscreen ? '退出全屏' : '全屏显示'"
                @click="toggleMapFullscreen"
              >
              </el-button>
            </div>
            <div class="chart-card">
              <h4 class="text-center mt-1">Normalized Abundance</h4>
              <div
                class="d-flex align-items-center justify-content-center gap-12 mt-3"
              >
                <div>
                  <div class="size">0.0001%</div>
                  <div class="cricle" style="width: 7px; height: 7px"></div>
                </div>
                <div>
                  <div class="size">0.001%</div>
                  <div class="cricle" style="width: 11px; height: 11px"></div>
                </div>
                <div>
                  <div class="size">0.01%</div>
                  <div class="cricle" style="width: 17px; height: 17px"></div>
                </div>
                <div>
                  <div class="size">0.1%</div>
                  <div class="cricle" style="width: 21px; height: 21px"></div>
                </div>
                <div>
                  <div class="size">1%</div>
                  <div class="cricle" style="width: 25px; height: 25px"></div>
                </div>
                <div>
                  <div class="size">10%</div>
                  <div class="cricle" style="width: 30px; height: 30px"></div>
                </div>
                <div>
                  <div class="size">100%</div>
                  <div class="cricle" style="width: 35px; height: 35px"></div>
                </div>
              </div>
            </div>
          </div>
          <div
            v-show="form.analysisType === 'species-diversity'"
            class="card mb-1 pos-relative"
          >
            <div class="chart-container">
              <div class="text-center font-18 font-600">
                Taxonomic Composition of Selected Samples
                <el-icon color="#1F77B4" class="ml-05 cursor-pointer"
                  ><Download
                /></el-icon>
              </div>
              <div
                id="chart01"
                ref="barRef"
                style="width: 100%; height: 450px"
              ></div>
              <div class="mt-2 d-flex">
                <div class="w-50">
                  <div class="text-center font-18 font-600">
                    A barplot of the LDA values distribution, LDA >
                    2,P-value<0.05
                    <el-icon color="#1F77B4" class="ml-05 cursor-pointer"
                      ><Download
                    /></el-icon>
                  </div>
                  <div
                    id="ldaChart"
                    ref="ldaRef"
                    style="width: 100%; height: 500px"
                  ></div>
                </div>

                <div class="w-50">
                  <div class="text-center font-18 font-600">
                    Taxonomic Composition of Selected Samples
                    <el-icon color="#1F77B4" class="ml-05 cursor-pointer"
                      ><Download
                    /></el-icon>
                  </div>
                  <img
                    src="@/assets/images/diver-distances.png"
                    style="width: 100%"
                    alt=""
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="card pos-relative">
            <div class="d-flex justify-space-between align-items-center">
              <h3 class="mb-0 mt-0">Sample List</h3>
              <el-popover
                placement="bottom-end"
                :width="300"
                trigger="hover"
                popper-class="metadata-popover"
              >
                <template #reference>
                  <el-button type="success"> Add other metadata </el-button>
                </template>
                <div class="metadata-selector">
                  <h4 class="metadata-title">Select Columns to Display</h4>
                  <div class="column-checkboxes">
                    <el-checkbox
                      v-for="column in allColumns"
                      :key="column.prop"
                      v-model="column.visible"
                      :label="column.label"
                      class="column-checkbox"
                    />
                  </div>
                </div>
              </el-popover>
            </div>
            <el-divider class="mt-1"></el-divider>
            <el-table
              ref="table"
              height="500"
              tooltip-effect="dark"
              :data="dataTable"
              :header-cell-style="{
                backgroundColor: '#F1F5F9',
                color: '#333333',
                fontWeight: 700,
              }"
              border
              :stripe="true"
            >
              <el-table-column type="selection" width="55" />

              <!-- Default visible columns -->
              <el-table-column
                v-if="getColumnVisibility('runId')"
                label="Run ID"
                prop="runId"
                width="140"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('bioProjectId')"
                label="BioProject ID"
                prop="projectId"
                width="150"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('latitude')"
                label="Latitude"
                prop="latitude"
                width="120"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('longitude')"
                label="Longitude"
                prop="longitude"
                width="120"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('hydrosphereType')"
                label="Hydrosphere Type"
                prop="hydrosphereType"
                width="160"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('geolocation')"
                label="Geolocation"
                prop="geolocation"
                width="150"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('waterBodyType')"
                label="Water Body Type"
                prop="waterBodyType"
                width="160"
              ></el-table-column>

              <!-- Hidden columns that can be toggled -->
              <el-table-column
                v-if="getColumnVisibility('depth')"
                label="Depth"
                prop="depth"
                width="100"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('temperature')"
                label="Temperature"
                prop="temperature"
                width="120"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('salinity')"
                label="Salinity"
                prop="salinity"
                width="100"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('ph')"
                label="pH"
                prop="ph"
                width="80"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('criticalZone')"
                label="Critical Zone"
                prop="criticalZone"
                width="120"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('samplingSubstrate')"
                label="Sampling Substrate"
                prop="samplingSubstrate"
                width="150"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('country')"
                label="Country"
                prop="country"
                width="120"
              ></el-table-column>

              <el-table-column
                v-if="getColumnVisibility('waterBodyName')"
                label="Water Body Name"
                prop="waterBodyName"
                width="160"
              ></el-table-column>

              <!-- Analysis Results column (always visible) -->
              <el-table-column
                v-if="getColumnVisibility('analysisResults')"
                label="Analysis Results"
                prop="analysisResults"
                width="160"
                align="center"
              >
                <template #default="scope">
                  <router-link to="/diversity/detail">
                    <div class="text-primary">View</div>
                  </router-link>
                </template>
              </el-table-column>
            </el-table>

            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              class="mb-1 mt-2 justify-center"
              :page-sizes="[100, 200, 300, 400]"
              layout="total, sizes, prev, pager, next"
              :total="dataTable.length"
            />
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
  import { nextTick, onMounted, reactive, ref } from 'vue';
  import {
    Plus,
    Minus,
    Promotion,
    UploadFilled,
    FullScreen,
    Rank,
  } from '@element-plus/icons-vue';
  import L from 'leaflet';
  import 'leaflet/dist/leaflet.css';
  import * as echarts from 'echarts';
  import ocean from '../../../public/geojson/ocean.json';
  import lakes from '../../../public/geojson/sample_lakes.json';
  import rivers from '../../../public/geojson/sample_rivers.json';
  import dataSamples from '@/assets/geojson/biota_analysis_samples.js';
  import UnifiedCart from '@/components/ShoppingCart/UnifiedCart.vue';

  const form = reactive({
    analysisType: 'biogeography',
    domain: 'bacteria',
    taxonomy: 'species',
    speciesNames: [],
    sliderLongitude: [-180.0, 180.0],
    sliderLatitude: [-90, 90],
    longitudeTo: -180.0,
    longitudeFrom: 180.0,
    latitudeTo: -90,
    latitudeFrom: 90,
    hydrosphereType: 'marine',
    geolocation: '',
    runListText: '',
    diversityGroups: [
      {
        id: 1,
        name: 'Group A',
        sliderLongitude: [-180.0, 180.0],
        sliderLatitude: [-90, 90],
        longitudeTo: -180.0,
        longitudeFrom: 180.0,
        latitudeTo: -90,
        latitudeFrom: 90,
        hydrosphereType: 'marine',
        geolocation: '',
        runListText: '',
      },
    ],
  });
  const geolocationOptions = reactive([
    {
      label: 'Africa',
      value: 'Africa',
    },
    {
      label: 'Antarctica',
      value: 'Antarctica',
    },
    {
      label: 'Arctic Ocean',
      value: 'Arctic Ocean',
    },
    {
      label: 'Asia',
      value: 'Asia',
    },
    {
      label: 'Baltic Sea',
      value: 'Baltic Sea',
    },
    {
      label: 'Europe',
      value: 'Europe',
    },
    {
      label: 'Indian Ocean',
      value: 'Indian Ocean',
    },
    {
      label: 'Mediterranean Sea',
      value: 'Mediterranean Sea',
    },
    {
      label: 'North America',
      value: 'North America',
    },
    {
      label: 'North Atlantic',
      value: 'North Atlantic',
    },
    {
      label: 'North Pacific',
      value: 'North Pacific',
    },
    {
      label: 'Oceania',
      value: 'Oceania',
    },
    {
      label: 'South America',
      value: 'South America',
    },
    {
      label: 'South Atlantic',
      value: 'South Atlantic',
    },
    {
      label: 'South Pacific',
      value: 'South Pacific',
    },
    {
      label: 'Southern Ocean',
      value: 'Southern Ocean',
    },
    {
      label: 'Unclassfied',
      value: 'Unclassfied',
    },
  ]);
  const speciesNameOptions = reactive([
    {
      label: 'Escherichia coli',
      value: 'Escherichia coli',
    },
    {
      label: 'Staphylococcus aureus',
      value: 'Staphylococcus aureus',
    },
  ]);

  const table = ref(null);
  const currentPage = ref(1);
  const pageSize = ref(10);

  // 购物车选择相关
  const selectedCartCount = ref(0);

  const barRef = ref(null);
  let barInstance = null;

  const ldaRef = ref(null);
  let ldaInstance = null;

  const summarySelect = ref(
    'K00237; SDHD,SDH4; succinate dehydrogenase (ubiquinone) membrane anchor subunit',
  );
  const isMapFullscreen = ref(false);

  // 全屏切换方法
  function toggleMapFullscreen() {
    isMapFullscreen.value = !isMapFullscreen.value;
    nextTick(() => {
      setTimeout(() => {
        const mapElement = document.getElementById('biotaMap');
        if (mapElement && window.mapInstance) {
          window.mapInstance.invalidateSize();
        }
      }, 300);
    });
  }

  // 添加分组
  function addGroup() {
    const newGroupId = form.diversityGroups.length + 1;
    const groupLetter = String.fromCharCode(64 + newGroupId); // A, B, C, etc.

    form.diversityGroups.push({
      id: newGroupId,
      name: `Group ${groupLetter}`,
      sliderLongitude: [-180.0, 180.0],
      sliderLatitude: [-90, 90],
      longitudeTo: -180.0,
      longitudeFrom: 180.0,
      latitudeTo: -90,
      latitudeFrom: 90,
      hydrosphereType: 'marine',
      geolocation: '',
      runListText: '',
    });
  }

  // 移除分组
  function removeGroup(index) {
    if (form.diversityGroups.length > 1) {
      form.diversityGroups.splice(index, 1);
    }
  }

  const dataTable = reactive([
    {
      runId: 'SRR25728811',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'South Pacific',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      runId: 'SRR25728812',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'South Pacific',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      runId: 'SRR25728814',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'South Pacific',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      runId: 'OER00094854',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'South Pacific',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      runId: 'SRR28427058',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'South Pacific',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      runId: 'SRR16472921',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'South Pacific',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      runId: 'SRR23495137',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'South Pacific',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      runId: 'SRR25728903',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'South Pacific',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      runId: 'SRR25728899',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'South Pacific',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      runId: 'SRR7663137',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'South Pacific',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
  ]);

  // 显示隐藏列
  const allColumns = reactive([
    { prop: 'runId', label: 'Run ID', visible: true, default: true },
    {
      prop: 'bioProjectId',
      label: 'BioProject ID',
      visible: true,
      default: true,
    },
    { prop: 'latitude', label: 'Latitude', visible: true, default: true },
    { prop: 'longitude', label: 'Longitude', visible: true, default: true },
    {
      prop: 'hydrosphereType',
      label: 'Hydrosphere Type',
      visible: true,
      default: true,
    },
    { prop: 'geolocation', label: 'Geolocation', visible: true, default: true },
    {
      prop: 'waterBodyType',
      label: 'Water Body Type',
      visible: true,
      default: true,
    },
    {
      prop: 'analysisResults',
      label: 'Analysis Results',
      visible: true,
      default: true,
    },
    { prop: 'depth', label: 'Depth', visible: false, default: false },
    {
      prop: 'temperature',
      label: 'Temperature',
      visible: false,
      default: false,
    },
    { prop: 'salinity', label: 'Salinity', visible: false, default: false },
    { prop: 'ph', label: 'pH', visible: false, default: false },
    {
      prop: 'criticalZone',
      label: 'Critical Zone',
      visible: false,
      default: false,
    },
    {
      prop: 'samplingSubstrate',
      label: 'Sampling Substrate',
      visible: false,
      default: false,
    },
    { prop: 'country', label: 'Country', visible: false, default: false },
    {
      prop: 'waterBodyName',
      label: 'Water Body Name',
      visible: false,
      default: false,
    },
  ]);

  function getColumnVisibility(prop) {
    const column = allColumns.find(col => col.prop === prop);
    return column ? column.visible : false;
  }

  const initMap = id => {
    var latlng = L.latLng(30, 110);
    var map = L.map(id, {
      // crs: L.CRS.Simple,
      center: latlng,
      zoom: 4,
      minZoom: 2, // 设置最小缩放级别为 10
      maxZoom: 18, // 设置最小缩放级别为 10
      // layers: [tiles],
      zoomControl: false,
      attributionControl: false,
      maxBounds: [
        [-90, -180],
        [90, 180],
      ],
    });

    map.createPane('oceanPane');
    map.createPane('riverPane');
    map.createPane('pointsPane');

    map.getPane('oceanPane').style.zIndex = 300; // 海洋图层
    map.getPane('riverPane').style.zIndex = 400; // 河流图层
    map.getPane('pointsPane').style.zIndex = 500; // 圆点图层

    const canvasRenderer = L.canvas({ padding: 0.5 });

    L.geoJSON(ocean, {
      onEachFeature: function (feature, layer) {
        let labelLatLng;
        // 根据特征名称选择标签位置
        if (feature.properties.name === 'North Pacific Ocean') {
          labelLatLng = L.latLng(30, -150);
        } else if (feature.properties.name === 'South Pacific Ocean') {
          labelLatLng = L.latLng(-30, -140);
        } else {
          // 默认使用中心点
          labelLatLng = layer.getBounds().getCenter();
        }

        // 创建一个标记
        var label = L.marker(labelLatLng, {
          icon: L.divIcon({
            className: 'ocean-label',
            html: feature.properties.name,
            iconSize: [100, 20],
          }),
        });
        label.addTo(map); // 将标签添加到地图
      },

      style: function () {
        return {
          fillColor: '#1C4F80', // 设置填充颜色为蓝色
          weight: 1,
          opacity: 1, // 不透明度设置为 1
          color: 'rgba(0, 0, 0, 0)', // 边界颜色设置为透明
          fillOpacity: 1, // 填充不透明度
          pane: 'oceanPane',
          renderer: canvasRenderer, // 使用 Canvas 渲染
        };
      },
    }).addTo(map);

    // var lakeLayer = null;
    L.geoJSON(lakes, {
      onEachFeature: function (feature, layer) {
        // 创建一个标记
        var label = L.marker(layer.getBounds().getCenter(), {
          icon: L.divIcon({
            iconSize: [100, 20],
          }),
        });
        label.addTo(map); // 将标签添加到地图
        map.on('zoomend', function () {
          let zoom = map.getZoom();
          label.setIcon(
            L.divIcon({
              className: 'lake-label',
              html: zoom > 4 ? feature.properties.Name : '',
            }),
          );
        });
      },

      style: function () {
        return {
          fillColor: '#9ABAE7', // 设置填充颜色为蓝色
          weight: 1,
          opacity: 1, // 不透明度设置为 1
          color: 'rgba(0, 0, 0, 0)', // 边界颜色设置为透明
          fillOpacity: 1, // 填充不透明度
          pane: 'oceanPane',
          renderer: canvasRenderer, // 使用 Canvas 渲染
        };
      },
    }).addTo(map);

    L.geoJSON(rivers, {
      onEachFeature: function (feature, layer) {
        // 创建一个标记
        var label = L.marker(layer.getBounds().getCenter(), {
          icon: L.divIcon({
            iconSize: [100, 20],
          }),
        });
        label.addTo(map); // 将标签添加到地图
        map.on('zoomend', function () {
          let riverZoom = map.getZoom();
          label.setIcon(
            L.divIcon({
              className: 'lake-label',
              html: riverZoom > 4 ? feature.properties.name : '',
            }),
          );
        });
      },
      style: function () {
        return {
          color: '#9ABAE7',
          opacity: 1,
          weight: 1,
          fillOpacity: 1,
          pane: 'riverPane',
          renderer: canvasRenderer, // 使用 Canvas 渲染
        };
      },
    }).addTo(map);

    // 创建圆点
    dataSamples.forEach((sample, index) => {
      if (index <= 1000) {
        const size = getSize(sample.abundance);
        const marker = L.circleMarker([sample.lat, sample.lon], {
          radius: size, // 根据丰度设置圆的大小
          fillColor: 'orange ',
          color: 'orange',
          weight: 1,
          opacity: 1,
          fillOpacity: 1,
        });

        // 创建工具提示内容
        const popupContent = `
 Number of Samples: 1<br>
            Latitude: ${sample.lat}<br>
            Longitude: ${sample.lon}<br>
             Abundance: ${sample.abundance}%<br>
             size: ${size}<br>
          `;
        // 绑定工具提示
        marker.bindTooltip(popupContent);

        marker.addTo(map);
      }
    });

    // 保存地图实例到全局变量，用于全屏切换时调整大小
    window.mapInstance = map;
  };

  function getSize(abundance) {
    var size = null;
    if (abundance < 0.0001 || (abundance >= 0.0001 && abundance < 0.001)) {
      size = 4;
    }
    if (abundance >= 0.001 && abundance < 0.01) {
      size = 6;
    }
    if (abundance >= 0.01 && abundance < 0.1) {
      size = 8;
    }
    if (abundance >= 0.1 && abundance < 1) {
      size = 10;
    }
    if (abundance >= 1 && abundance < 10) {
      size = 12;
    }
    if (abundance >= 10 && abundance < 100) {
      size = 14;
    }
    if (abundance >= 100) {
      size = 16;
    }
    return size;
  }

  // 堆积柱形图
  function initStackedBarChart() {
    const chartDom = document.getElementById('chart01');
    if (!chartDom) return;
    const myChart = echarts.init(chartDom);

    const mockApiData = {
      GroupA: {
        Thaumarchaeota: [
          ['SRR7648270', 'GroupA', 'Thaumarchaeota', '69.6221'],
          ['SRR7648271', 'GroupA', 'Thaumarchaeota', '76.4788'],
          ['SRR7648272', 'GroupA', 'Thaumarchaeota', '67.8934'],
          ['SRR7648273', 'GroupA', 'Thaumarchaeota', '71.2456'],
          ['SRR7648274', 'GroupA', 'Thaumarchaeota', '68.9123'],
          ['SRR7648275', 'GroupA', 'Thaumarchaeota', '84.5678'],
          ['SRR7648276', 'GroupA', 'Thaumarchaeota', '85.1234'],
          ['SRR7648277', 'GroupA', 'Thaumarchaeota', '54.7890'],
        ],
        Euryarchaeota: [
          ['SRR7648270', 'GroupA', 'Euryarchaeota', '25.1234'],
          ['SRR7648271', 'GroupA', 'Euryarchaeota', '18.2345'],
          ['SRR7648272', 'GroupA', 'Euryarchaeota', '26.7890'],
          ['SRR7648273', 'GroupA', 'Euryarchaeota', '23.4567'],
          ['SRR7648274', 'GroupA', 'Euryarchaeota', '25.8901'],
          ['SRR7648275', 'GroupA', 'Euryarchaeota', '12.3456'],
          ['SRR7648276', 'GroupA', 'Euryarchaeota', '11.7890'],
          ['SRR7648277', 'GroupA', 'Euryarchaeota', '37.8901'],
        ],
        Crenarchaeota: [
          ['SRR7648270', 'GroupA', 'Crenarchaeota', '3.1234'],
          ['SRR7648271', 'GroupA', 'Crenarchaeota', '2.2345'],
          ['SRR7648272', 'GroupA', 'Crenarchaeota', '2.7890'],
          ['SRR7648273', 'GroupA', 'Crenarchaeota', '2.8456'],
          ['SRR7648274', 'GroupA', 'Crenarchaeota', '2.3901'],
          ['SRR7648275', 'GroupA', 'Crenarchaeota', '1.5456'],
          ['SRR7648276', 'GroupA', 'Crenarchaeota', '1.8890'],
          ['SRR7648277', 'GroupA', 'Crenarchaeota', '3.6901'],
        ],
        Others: [
          ['SRR7648270', 'GroupA', 'Others', '2.1311'],
          ['SRR7648271', 'GroupA', 'Others', '3.0522'],
          ['SRR7648272', 'GroupA', 'Others', '2.5286'],
          ['SRR7648273', 'GroupA', 'Others', '2.4521'],
          ['SRR7648274', 'GroupA', 'Others', '2.8175'],
          ['SRR7648275', 'GroupA', 'Others', '1.541'],
          ['SRR7648276', 'GroupA', 'Others', '1.1986'],
          ['SRR7648277', 'GroupA', 'Others', '3.6308'],
        ],
      },
      GroupB: {
        Thaumarchaeota: [
          ['SRR7648281', 'GroupB', 'Thaumarchaeota', '70.8234'],
          ['SRR7648282', 'GroupB', 'Thaumarchaeota', '77.5678'],
          ['SRR7648283', 'GroupB', 'Thaumarchaeota', '69.9012'],
          ['SRR7648284', 'GroupB', 'Thaumarchaeota', '80.3456'],
          ['SRR7648285', 'GroupB', 'Thaumarchaeota', '51.7890'],
          ['SRR7648286', 'GroupB', 'Thaumarchaeota', '48.2345'],
          ['SRR7648287', 'GroupB', 'Thaumarchaeota', '71.6789'],
          ['SRR7648288', 'GroupB', 'Thaumarchaeota', '57.9012'],
        ],
        Euryarchaeota: [
          ['SRR7648281', 'GroupB', 'Euryarchaeota', '24.1234'],
          ['SRR7648282', 'GroupB', 'Euryarchaeota', '18.3456'],
          ['SRR7648283', 'GroupB', 'Euryarchaeota', '25.7890'],
          ['SRR7648284', 'GroupB', 'Euryarchaeota', '15.2345'],
          ['SRR7648285', 'GroupB', 'Euryarchaeota', '41.6789'],
          ['SRR7648286', 'GroupB', 'Euryarchaeota', '44.8012'],
          ['SRR7648287', 'GroupB', 'Euryarchaeota', '23.4567'],
          ['SRR7648288', 'GroupB', 'Euryarchaeota', '35.8901'],
        ],
        Crenarchaeota: [
          ['SRR7648281', 'GroupB', 'Crenarchaeota', '2.5234'],
          ['SRR7648282', 'GroupB', 'Crenarchaeota', '2.1456'],
          ['SRR7648283', 'GroupB', 'Crenarchaeota', '2.8890'],
          ['SRR7648284', 'GroupB', 'Crenarchaeota', '2.0345'],
          ['SRR7648285', 'GroupB', 'Crenarchaeota', '3.2789'],
          ['SRR7648286', 'GroupB', 'Crenarchaeota', '3.6012'],
          ['SRR7648287', 'GroupB', 'Crenarchaeota', '2.5567'],
          ['SRR7648288', 'GroupB', 'Crenarchaeota', '3.1901'],
        ],
        Others: [
          ['SRR7648281', 'GroupB', 'Others', '2.5532'],
          ['SRR7648282', 'GroupB', 'Others', '2.041'],
          ['SRR7648283', 'GroupB', 'Others', '1.422'],
          ['SRR7648284', 'GroupB', 'Others', '2.3854'],
          ['SRR7648285', 'GroupB', 'Others', '3.2532'],
          ['SRR7648286', 'GroupB', 'Others', '3.3631'],
          ['SRR7648287', 'GroupB', 'Others', '2.3077'],
          ['SRR7648288', 'GroupB', 'Others', '3.0186'],
        ],
      },
    };

    // 提取所有物种类别和颜色配置
    const allSpecies = new Set();
    Object.keys(mockApiData).forEach(groupName => {
      Object.keys(mockApiData[groupName]).forEach(species => {
        allSpecies.add(species);
      });
    });

    const categories = Array.from(allSpecies);
    const colors = {
      Thaumarchaeota: '#1f77b4',
      Euryarchaeota: '#ff7f0e',
      Crenarchaeota: '#d62728',
      Candidatus: '#2ca02c',
      Nanoarchaeota: '#17becf',
      Others: '#7f7f7f',
    };

    // 处理数据，按Group和Sample组织
    function processApiData(apiData) {
      const processedData = {};

      Object.keys(apiData).forEach(groupName => {
        const groupData = apiData[groupName];
        const sampleMap = new Map();

        // 收集该Group的所有样本ID
        Object.keys(groupData).forEach(species => {
          groupData[species].forEach(item => {
            const [sampleId, group, speciesName, abundance] = item;
            if (!sampleMap.has(sampleId)) {
              sampleMap.set(sampleId, {});
            }
            sampleMap.get(sampleId)[speciesName] = parseFloat(abundance);
          });
        });

        processedData[groupName] = {
          samples: Array.from(sampleMap.keys()).sort(),
          data: sampleMap,
        };
      });

      return processedData;
    }

    const processedData = processApiData(mockApiData);
    const groupALabels = processedData.GroupA.samples;
    const groupBLabels = processedData.GroupB.samples;

    const groupASeries = categories.map(species => ({
      name: species,
      type: 'bar',
      stack: 'groupA',
      xAxisIndex: 0,
      yAxisIndex: 0,
      data: groupALabels.map(sampleId => {
        const sampleData = processedData.GroupA.data.get(sampleId);
        return sampleData && sampleData[species] ? sampleData[species] : 0;
      }),
      itemStyle: {
        color: colors[species] || '#7f7f7f',
      },
    }));

    const groupBSeries = categories.map(species => ({
      name: `${species}_B`,
      type: 'bar',
      stack: 'groupB',
      xAxisIndex: 1,
      yAxisIndex: 1,
      data: groupBLabels.map(sampleId => {
        const sampleData = processedData.GroupB.data.get(sampleId);
        return sampleData && sampleData[species] ? sampleData[species] : 0;
      }),
      itemStyle: {
        color: colors[species] || '#7f7f7f',
      },
      showInLegend: false,
    }));

    const allSeries = [...groupASeries, ...groupBSeries];

    const option = {
      title: {
        text: '',
        left: 'center',
      },
      tooltip: {
        show: true,
      },
      legend: {
        data: categories,
        bottom: 0,
        itemWidth: 12,
        itemHeight: 12,
      },
      grid: [
        {
          left: '4%',
          right: '51%',
          bottom: '20%',
          top: '10%',
        },
        {
          left: '51%',
          right: '4%',
          bottom: '20%',
          top: '10%',
        },
      ],
      xAxis: [
        {
          type: 'category',
          data: groupALabels,
          gridIndex: 0,
          axisLabel: {
            rotate: 45,
            fontSize: 10,
          },
        },
        {
          type: 'category',
          data: groupBLabels,
          gridIndex: 1,
          axisLabel: {
            rotate: 45,
            fontSize: 10,
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: 'Taxonomic Composition',
          max: 100,
          gridIndex: 0,
          axisLabel: {
            formatter: '{value}',
          },
          nameLocation: 'center',
          nameRotate: 90,
          nameGap: 30,
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'normal',
          },
        },
        {
          type: 'value',
          name: 'Taxonomic Composition',
          max: 100,
          show: false,
          gridIndex: 1,
          axisLabel: {
            formatter: '{value}',
          },
          nameLocation: 'center',
          nameRotate: 90,
          nameGap: 30,
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'normal',
          },
        },
      ],
      series: allSeries,
      graphic: [
        {
          type: 'text',
          left: '23%',
          top: '0%',
          style: {
            text: 'GroupA',
            fontSize: 16,
            fontWeight: 'bold',
            fill: '#1f77b4',
          },
        },
        {
          type: 'text',
          left: '73%',
          top: '0%',
          style: {
            text: 'GroupB',
            fontSize: 16,
            fontWeight: 'bold',
            fill: '#ff7f0e',
          },
        },
      ],
    };

    myChart.setOption(option);

    // Handle window resize
    window.addEventListener('resize', () => {
      myChart.resize();
    });
  }

  // 初始化LDA柱形图 - 单grid完整X轴实现
  function initLDAChart() {
    const chartDom = document.getElementById('ldaChart');
    if (!chartDom) {
      console.error('LDA Chart container not found!');
      return;
    }

    const myChart = echarts.init(chartDom);

    // 按照图片顺序排列所有数据
    const allGroups = [
      { name: 'Candidatus', value: -0.8, group: 'Control' },
      { name: 'Roseburia', value: -1.2, group: 'Control' },
      { name: 'Verrucomicrobiaceae', value: -1.6, group: 'Control' },
      { name: 'Verrucomicrobia', value: -2.0, group: 'Control' },
      { name: 'Akkermansia', value: -2.4, group: 'Control' },
      { name: 'Verrucomicrobiales', value: -2.8, group: 'Control' },
      { name: 'Verrucomicrobiae', value: -3.2, group: 'Control' },
      { name: 'Verrucomicrobiae2', value: -5, group: 'Control' },

      { name: 'Bacteroidales', value: 4.8, group: 'Treatment' },
      { name: 'Bacteroidetes', value: 4.6, group: 'Treatment' },
      { name: 'Bacteroidia', value: 4.4, group: 'Treatment' },
      { name: 'Parabacteroides', value: 4.2, group: 'Treatment' },
      { name: 'Parasutterella', value: 4.0, group: 'Treatment' },
      { name: 'Sutterellaceae', value: 3.8, group: 'Treatment' },
      { name: 'Betaproteobacteria', value: 3.6, group: 'Treatment' },
    ];

    const option = {
      tooltip: {},
      grid: {
        left: '-12%',
        right: '10%',
        bottom: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        textStyle: {
          fontSize: 14,
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
          },
        },
      },
      // 单一Y轴，显示所有物种
      yAxis: {
        type: 'category',
        data: allGroups.map(item => item.name),
        show: false,
      },
      // 单一系列，根据数值正负显示不同颜色
      series: [
        {
          type: 'bar',
          data: allGroups.map(item => ({
            value: item.value,
            label: {
              show: true,
              position: item.group === 'Treatment' ? 'left' : 'right',
              formatter: item.name,
            },
            itemStyle: {
              color: item.group === 'Treatment' ? '#3498DB' : ' #FFA500',
            },
          })),
          barWidth: 20,
          itemStyle: {
            borderRadius: 0,
          },
        },
      ],
      // 添加底部X轴标签
      graphic: [
        {
          type: 'text',
          left: 'center',
          bottom: '5%',
          style: {
            text: 'LDA SCORE (log 10)',
            fontSize: 12,
            fill: '#333',
            textAlign: 'center',
          },
        },
      ],
    };

    myChart.setOption(option);

    // 处理窗口大小变化
    window.addEventListener('resize', () => {
      myChart.resize();
    });

    return myChart;
  }

  function handleAnalysisTypeChange() {
    nextTick(() => {
      if (form.analysisType === 'species-diversity') {
        barInstance = echarts.init(barRef.value);
        ldaInstance = echarts.init(ldaRef.value);
        barInstance?.resize();
        ldaInstance?.resize();
      }
    });
  }

  // 处理购物车选择
  const handleCartSelection = data => {
    selectedCartCount.value = data.count;
  };

  onMounted(() => {
    nextTick(() => {
      // 根据初始选择的分析类型初始化相应视图
      initStackedBarChart();
      initLDAChart();
      initMap('biotaMap');
      // }
    });
  });
</script>

<style lang="scss" scoped>
  .submit-page {
    padding: 120px 0 45px 0;
  }

  :deep(.el-slider__bar),
  .filter-search {
    background-color: #1e7cb2;
  }

  :deep(.el-slider__button) {
    width: 12px;
    height: 12px;
  }

  h3 {
    display: flex;
    align-items: center;
    color: #1e7cb2;
  }

  .filter-svg {
    width: 18px;
    height: 18px;
  }

  :deep(.el-popper.is-dark) {
    width: 300px;
  }

  .svg {
    width: 14px;
    height: 14px;
    margin-right: 0.3rem;
  }

  .filter {
    width: 24px;
    height: 26px;
    margin-right: 0.5rem;
  }

  :deep(.el-form-item__label) {
    font-weight: 600;
  }

  :deep(.leaflet-marker-icon.ocean-label) {
    color: #ffffff;
    font-size: 15px;
    width: 200px !important;
    z-index: 200;
  }

  .lake-label {
    color: #2668b4;
    font-family: initial;
  }

  :deep(.el-upload-dragger) {
    padding: 10px 0;
  }
  :deep(.el-upload-dragger .el-icon--upload) {
    color: #2668b4;
    font-size: 45px;
    margin-bottom: 0;
  }

  :deep(.el-upload-list) {
    margin: 0 !important;
  }

  :deep(.el-button--small.is-round) {
    width: 24px;
    height: 24px;
    font-size: 14px;
    padding: 0;
  }

  .legend {
    //position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 15px;
  }

  .cricle {
    background-color: #f3a91d;
    border-radius: 50%;
  }

  .legend-item {
    display: flex;
    align-items: center;
    justify-content: space-between;

    & > div {
      margin-right: 18px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }

  h4 {
    text-align: center;
    font-size: 1.1rem;
    margin-right: 20px;
  }

  lake-label {
    color: #2668b4;
    font-family: initial;
  }

  :deep(.lake-label) {
    color: #2668b4;
    font-family: initial;
    white-space: nowrap;
  }

  :deep(.leaflet-div-icon) {
    background: transparent;
    border: none;
  }

  /* New styles for the updated form */
  .species-section {
    padding: 0 0 0 16px;
  }

  .species-warning {
    color: #f56c6c;
    font-size: 12px;
    margin-top: 4px;
    line-height: 1.4;
  }

  .data-filter-section {
    padding: 0 4px 0 16px;
    flex: 1;
  }

  .diversity-group {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 16px;
    background-color: #ffffff;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .group-header {
    border-bottom: 1px solid #e4e7ed;
    padding-bottom: 8px;
    margin-bottom: 16px;
  }

  .group-title {
    color: #1e7cb2;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
  }

  .group-actions {
    display: flex;
  }

  .group-content {
    .el-form-item {
      margin-bottom: 12px;
    }
  }

  .upload-section {
    flex: 1;
    .upload-demo {
      margin-top: 8px;
    }
  }

  .mb-2 {
    margin-bottom: 12px !important;
  }

  .mb-3 {
    margin-bottom: 20px !important;
  }

  :deep(.el-radio-group) {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
  }

  :deep(.el-radio) {
    margin-right: 0;
  }

  /* Section label styling */
  .section-label {
    font-size: 16px;
    font-weight: 600;
    color: #1e7cb2;
    border-bottom: 2px solid #1e7cb2;
    padding-bottom: 4px;
    margin-bottom: 8px;
    display: inline-block;
  }

  /* Custom radio button styling */
  .custom-radio-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    :deep(.el-radio-button) {
      margin-right: 0;
    }

    :deep(.el-radio-button__inner) {
      background-color: #f5f5f5;
      border: 1px solid #dcdfe6;
      color: #606266;
      padding: 4px 10px !important;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background-color: #3498db;
        border-color: #3498db;
        color: #ffffff;
      }
    }

    :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
      background-color: #3498db;
      border-color: #3498db;
      color: #ffffff;
      box-shadow: none;
    }

    :deep(.el-radio-button:first-child .el-radio-button__inner) {
      border-radius: 4px;
    }

    :deep(.el-radio-button:last-child .el-radio-button__inner) {
      border-radius: 4px;
    }
  }

  /* Group title input styling */
  .group-title-input {
    :deep(.el-input__wrapper) {
      background-color: transparent;
      border: none;
      box-shadow: none;
      padding: 0;
    }

    :deep(.el-input__inner) {
      color: #1e7cb2;
      font-size: 16px;
      font-weight: 600;
      text-align: left;
    }
  }

  /* Metadata popover styling */
  :deep(.metadata-popover) {
    .metadata-selector {
      .metadata-title {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
        text-align: center;
      }

      .column-checkboxes {
        max-height: 300px;
        overflow-y: auto;
        margin-bottom: 16px;

        .column-checkbox {
          display: block;
          margin-bottom: 8px;

          :deep(.el-checkbox__label) {
            font-size: 14px;
            color: #606266;
          }
        }
      }

      .metadata-actions {
        text-align: center;
        border-top: 1px solid #e4e7ed;
        padding-top: 12px;
      }
    }
  }

  /* 购物车水平布局样式 */
  .cart-form-item {
    display: flex;
    align-items: center;
    gap: 12px;

    .cart-label {
      font-size: 14px;
      font-weight: 700;
      color: #606266;
      white-space: nowrap;
      min-width: fit-content;
    }

    .cart-section {
      flex: 1;
    }
  }

  /* 选中数据信息样式 */
  .selected-data-info {
    margin-top: 8px;
    padding-left: 0;

    .el-tag {
      font-size: 12px;
      padding: 4px 8px;
    }
  }

  /* map */
  .chart-card {
    position: absolute;
    right: 10px;
    bottom: 10px;
    color: #272728;
    background: rgba(255, 255, 255, 0.9);
    border-left: 1px solid #007ed31a;
    width: auto;
    padding: 5px 10px;
    opacity: 0.9;
    z-index: 999;
  }
</style>
