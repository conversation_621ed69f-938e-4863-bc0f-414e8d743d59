#!/usr/bin/env python3
"""
KO数据处理脚本

该脚本读取TSV文件（行为KO_ID，列为RUN_ID），以及KO_ID和RUN_ID的列表文件，
为每个KO_ID生成一个包含RUN_ID和对应值的TSV文件。

使用方法:
python process_ko_data.py <tsv_file> <ko_ids_file> <run_ids_file> [output_dir]

参数:
- tsv_file: 输入的TSV文件路径（行为KO_ID，列为RUN_ID）
- ko_ids_file: 包含KO_ID列表的文本文件路径（每行一个KO_ID）
- run_ids_file: 包含RUN_ID列表的文本文件路径（每行一个RUN_ID）
- output_dir: 输出目录（可选，默认为当前目录）
"""

import sys
import os
import pandas as pd
import argparse
from pathlib import Path


def read_id_list(file_path):
    """读取ID列表文件，返回ID列表"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            ids = [line.strip() for line in f if line.strip()]
        return ids
    except Exception as e:
        print(f"错误：无法读取文件 {file_path}: {e}")
        sys.exit(1)


def read_tsv_data(tsv_file):
    """读取TSV文件，返回DataFrame"""
    try:
        df = pd.read_csv(tsv_file, sep='\t', index_col=0)
        return df
    except Exception as e:
        print(f"错误：无法读取TSV文件 {tsv_file}: {e}")
        sys.exit(1)


def process_ko_data(tsv_file, ko_ids_file, run_ids_file, output_dir='.'):
    """
    处理KO数据的主函数
    
    Args:
        tsv_file: TSV数据文件路径
        ko_ids_file: KO_ID列表文件路径
        run_ids_file: RUN_ID列表文件路径
        output_dir: 输出目录路径
    """
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 读取数据
    print("正在读取数据文件...")
    ko_ids = read_id_list(ko_ids_file)
    run_ids = read_id_list(run_ids_file)
    data_df = read_tsv_data(tsv_file)
    
    print(f"读取到 {len(ko_ids)} 个KO_ID")
    print(f"读取到 {len(run_ids)} 个RUN_ID")
    print(f"TSV数据维度: {data_df.shape}")
    
    # 检查数据完整性
    missing_ko_ids = [ko_id for ko_id in ko_ids if ko_id not in data_df.index]
    missing_run_ids = [run_id for run_id in run_ids if run_id not in data_df.columns]
    
    if missing_ko_ids:
        print(f"警告：以下KO_ID在TSV文件中未找到: {missing_ko_ids[:10]}{'...' if len(missing_ko_ids) > 10 else ''}")
    
    if missing_run_ids:
        print(f"警告：以下RUN_ID在TSV文件中未找到: {missing_run_ids[:10]}{'...' if len(missing_run_ids) > 10 else ''}")
    
    # 为每个KO_ID生成结果文件
    print("正在生成结果文件...")
    processed_count = 0
    
    for ko_id in ko_ids:
        if ko_id not in data_df.index:
            print(f"跳过KO_ID {ko_id}（在TSV文件中未找到）")
            continue
        
        # 提取该KO_ID对应的数据
        ko_data = data_df.loc[ko_id]
        
        # 创建结果DataFrame
        result_data = []
        for run_id in run_ids:
            if run_id in ko_data.index:
                value = ko_data[run_id]
                result_data.append([run_id, value])
            else:
                # 如果RUN_ID不存在，可以选择跳过或填入默认值
                print(f"警告：RUN_ID {run_id} 在KO_ID {ko_id} 的数据中未找到")
                result_data.append([run_id, 'NA'])  # 或者使用 None, 0 等默认值
        
        # 创建结果DataFrame
        result_df = pd.DataFrame(result_data, columns=['RUN_ID', 'Value'])
        
        # 保存结果文件
        output_file = output_path / f"{ko_id}.tsv"
        result_df.to_csv(output_file, sep='\t', index=False)
        
        processed_count += 1
        if processed_count % 100 == 0:
            print(f"已处理 {processed_count} 个KO_ID...")
    
    print(f"处理完成！共生成 {processed_count} 个结果文件")
    print(f"输出目录: {output_path.absolute()}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="处理KO数据，为每个KO_ID生成包含RUN_ID和对应值的TSV文件",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python process_ko_data.py data.tsv ko_ids.txt run_ids.txt
  python process_ko_data.py data.tsv ko_ids.txt run_ids.txt output/
        """
    )
    
    parser.add_argument('tsv_file', help='输入的TSV文件路径（行为KO_ID，列为RUN_ID）')
    parser.add_argument('ko_ids_file', help='包含KO_ID列表的文本文件路径')
    parser.add_argument('run_ids_file', help='包含RUN_ID列表的文本文件路径')
    parser.add_argument('output_dir', nargs='?', default='.', 
                       help='输出目录（默认为当前目录）')
    
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    for file_path in [args.tsv_file, args.ko_ids_file, args.run_ids_file]:
        if not os.path.exists(file_path):
            print(f"错误：文件不存在: {file_path}")
            sys.exit(1)
    
    # 执行处理
    process_ko_data(args.tsv_file, args.ko_ids_file, args.run_ids_file, args.output_dir)


if __name__ == "__main__":
    main()
