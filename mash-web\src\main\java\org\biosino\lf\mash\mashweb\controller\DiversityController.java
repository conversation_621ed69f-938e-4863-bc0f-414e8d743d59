package org.biosino.lf.mash.mashweb.controller;

import lombok.RequiredArgsConstructor;
import org.biosino.lf.mash.mashweb.core.web.AjaxResult;
import org.biosino.lf.mash.mashweb.dto.BiogeographyCreateDTO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/7/21
 */
@RestController
@RequestMapping("/diversity")
@RequiredArgsConstructor
public class DiversityController extends BaseController {

    @RequestMapping("/biogeography")
    public AjaxResult createBiogeography(@RequestBody BiogeographyCreateDTO paramsDTO) {
        return AjaxResult.success();
    }

    @RequestMapping("/speciesDiversity")
    public AjaxResult createDiversity() {
        return AjaxResult.success();
    }
}
