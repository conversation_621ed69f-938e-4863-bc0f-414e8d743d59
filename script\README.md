# KO数据处理脚本使用说明

## 脚本功能

`process_ko_data.py` 脚本用于处理KO（KEGG Orthology）数据，将一个大的TSV表格按照KO_ID拆分成多个小文件。

## 输入文件格式

### 1. TSV数据文件
- 格式：制表符分隔的表格文件
- 行：KO_ID
- 列：RUN_ID
- 示例：
```
	RUN001	RUN002	RUN003
K00001	1.23	2.45	0.67
K00002	3.21	1.89	4.56
K00003	0.98	3.67	2.34
```

### 2. KO_ID列表文件
- 格式：纯文本文件，每行一个KO_ID
- 示例：
```
K00001
K00002
K00003
```

### 3. RUN_ID列表文件
- 格式：纯文本文件，每行一个RUN_ID
- 示例：
```
RUN001
RUN002
RUN003
```

## 使用方法

### 基本用法
```bash
python process_ko_data.py <tsv_file> <ko_ids_file> <run_ids_file>
```

### 指定输出目录
```bash
python process_ko_data.py <tsv_file> <ko_ids_file> <run_ids_file> <output_dir>
```

### 示例
```bash
# 在当前目录生成结果文件
python process_ko_data.py data.tsv ko_list.txt run_list.txt

# 在指定目录生成结果文件
python process_ko_data.py data.tsv ko_list.txt run_list.txt output/
```

## 输出结果

脚本会为每个KO_ID生成一个TSV文件，文件名格式为 `{ko_id}.tsv`。

每个输出文件包含两列：
- `RUN_ID`: 运行ID
- `Value`: 对应的数值

示例输出文件 `K00001.tsv`：
```
RUN_ID	Value
RUN001	1.23
RUN002	2.45
RUN003	0.67
```

## 依赖包

脚本需要以下Python包：
- pandas
- pathlib (Python标准库)

安装依赖：
```bash
pip install pandas
```

## 注意事项

1. 如果某个KO_ID在TSV文件中不存在，脚本会跳过并显示警告
2. 如果某个RUN_ID在特定KO_ID的数据中不存在，会填入 'NA' 值
3. 脚本会自动创建输出目录（如果不存在）
4. 处理大量数据时，脚本会每100个KO_ID显示一次进度

## 错误处理

- 文件不存在：脚本会显示错误信息并退出
- 文件格式错误：脚本会显示具体的错误信息
- 数据不匹配：脚本会显示警告但继续处理
