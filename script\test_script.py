#!/usr/bin/env python3
"""
测试脚本，用于验证 process_ko_data.py 的功能
"""

import os
import subprocess
import sys
from pathlib import Path

def run_test():
    """运行测试"""
    script_dir = Path(__file__).parent
    
    # 检查示例文件是否存在
    required_files = [
        'process_ko_data.py',
        'example_data.tsv',
        'example_ko_ids.txt',
        'example_run_ids.txt'
    ]
    
    for file_name in required_files:
        file_path = script_dir / file_name
        if not file_path.exists():
            print(f"错误：找不到文件 {file_path}")
            return False
    
    # 创建测试输出目录
    test_output_dir = script_dir / 'test_output'
    test_output_dir.mkdir(exist_ok=True)
    
    # 运行脚本
    cmd = [
        sys.executable,
        str(script_dir / 'process_ko_data.py'),
        str(script_dir / 'example_data.tsv'),
        str(script_dir / 'example_ko_ids.txt'),
        str(script_dir / 'example_run_ids.txt'),
        str(test_output_dir)
    ]
    
    print("运行命令:", ' '.join(cmd))
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=script_dir)
        
        print("标准输出:")
        print(result.stdout)
        
        if result.stderr:
            print("标准错误:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("脚本执行成功！")
            
            # 检查输出文件
            expected_files = ['K00001.tsv', 'K00002.tsv', 'K00003.tsv', 'K00004.tsv', 'K00005.tsv']
            
            print("\n检查输出文件:")
            for file_name in expected_files:
                file_path = test_output_dir / file_name
                if file_path.exists():
                    print(f"✓ {file_name} 已生成")
                    # 显示文件内容
                    with open(file_path, 'r') as f:
                        content = f.read()
                        print(f"  内容预览:\n{content[:200]}{'...' if len(content) > 200 else ''}")
                else:
                    print(f"✗ {file_name} 未生成")
            
            return True
        else:
            print(f"脚本执行失败，返回码: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"执行脚本时出错: {e}")
        return False

if __name__ == "__main__":
    print("开始测试 process_ko_data.py 脚本...")
    success = run_test()
    
    if success:
        print("\n测试完成！脚本工作正常。")
    else:
        print("\n测试失败！请检查脚本和输入文件。")
