<template>
  <!-- 购物车图标 - 根据variant显示不同样式 -->
  <div
    v-if="variant === 'floating'"
    class="floating-cart-icon"
    @click="showCartDialog = true"
  >
    <el-icon class="cart-icon">
      <ShoppingCart />
    </el-icon>
    <!-- 数量徽章 -->
    <el-badge :value="999" class="cart-badge" type="danger" />
  </div>

  <div v-else class="cart-icon-container">
    <div class="cart-icon-small" @click="showCartDialog = true">
      <el-icon class="icon">
        <ShoppingCart size="20" />
      </el-icon>
      <el-badge :value="999" class="badge" type="danger" />
    </div>
  </div>

  <!-- 购物车内容弹框 -->
  <el-dialog v-model="showCartDialog" width="1000px" :append-to-body="true">
    <el-tabs v-model="activeName" type="card" closable @edit="handleTabsEdit">
      <el-tab-pane name="first">
        <template #label>
          <div class="tab-label">
            <span v-if="!editingTab.first" @dblclick="startEditTab('first')">
              {{ groupNames.first }} : {{ groupAData.length }}
            </span>
            <el-input
              v-else
              ref="tabInput"
              v-model="tempGroupName"
              size="small"
              class="tab-input"
              @blur="finishEditTab('first')"
              @keyup.enter.stop="finishEditTab('first')"
              @keyup.esc.stop="cancelEditTab('first')"
              @keydown.stop
              @keyup.stop
              @click.stop
            />
          </div>
        </template>
        <el-table
          ref="table"
          height="500"
          tooltip-effect="dark"
          :data="paginatedData"
          :header-cell-style="{
            backgroundColor: '#F1F5F9',
            color: '#333333',
            fontWeight: 700,
          }"
          border
          :stripe="true"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />

          <!-- Default visible columns -->
          <el-table-column
            label="Run ID"
            prop="runId"
            width="140"
          ></el-table-column>

          <el-table-column
            label="BioProject ID"
            prop="projectId"
            width="150"
          ></el-table-column>

          <el-table-column
            label="Latitude"
            prop="latitude"
            width="120"
          ></el-table-column>

          <el-table-column
            label="Longitude"
            prop="longitude"
            width="120"
          ></el-table-column>

          <el-table-column
            label="Hydrosphere Type"
            prop="hydrosphereType"
            width="160"
          ></el-table-column>

          <el-table-column
            label="Geolocation"
            prop="geolocation"
            width="150"
          ></el-table-column>

          <el-table-column
            label="Water Body Type"
            prop="waterBodyType"
            width="160"
          ></el-table-column>

          <!-- Hidden columns that can be toggled -->
          <el-table-column
            label="Depth"
            prop="depth"
            width="100"
          ></el-table-column>

          <el-table-column
            label="Temperature"
            prop="temperature"
            width="120"
          ></el-table-column>

          <el-table-column
            label="Salinity"
            prop="salinity"
            width="100"
          ></el-table-column>

          <el-table-column label="pH" prop="ph" width="80"></el-table-column>

          <el-table-column
            label="Critical Zone"
            prop="criticalZone"
            width="120"
          ></el-table-column>

          <el-table-column
            label="Sampling Substrate"
            prop="samplingSubstrate"
            width="150"
          ></el-table-column>

          <el-table-column
            label="Country"
            prop="country"
            width="120"
          ></el-table-column>

          <el-table-column
            label="Water Body Name"
            prop="waterBodyName"
            width="160"
          ></el-table-column>
        </el-table>

        <!-- 操作按钮和分页 -->
        <div class="table-footer">
          <div class="action-buttons">
            <el-button type="warning" :disabled="selectedRows.length === 0">
              Clear Selection
            </el-button>
            <el-button type="danger" :disabled="selectedRows.length === 0">
              Delete Selected
            </el-button>
            <el-button
              v-show="variant === 'inline'"
              type="primary"
              :disabled="selectedRows.length === 0"
              @click="confirmSelection"
            >
              Confirm ({{ selectedRows.length }})
            </el-button>
          </div>

          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            class="pagination"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next"
            :total="groupAData.length"
          />
        </div>
      </el-tab-pane>

      <el-tab-pane name="second">
        <template #label>
          <div class="tab-label">
            <span v-if="!editingTab.second" @dblclick="startEditTab('second')">
              {{ groupNames.second }} : {{ groupBData.length }}
            </span>
            <el-input
              v-else
              ref="tabInput"
              v-model="tempGroupName"
              size="small"
              class="tab-input"
              @blur="finishEditTab('second')"
              @keyup.enter.stop="finishEditTab('second')"
              @keyup.esc.stop="cancelEditTab('second')"
              @keydown.stop
              @keyup.stop
              @click.stop
            />
          </div>
        </template>
        <div class="empty-group">
          <el-empty description="No data in this group" />
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script setup>
  import { reactive, ref, computed, nextTick } from 'vue';
  import { ShoppingCart } from '@element-plus/icons-vue';
  import { ElMessage, ElMessageBox } from 'element-plus';

  // Props
  const props = defineProps({
    variant: {
      type: String,
      default: 'floating',
      validator: value => ['floating', 'inline'].includes(value),
    },
  });

  // Emits
  const emit = defineEmits(['confirm-selection']);
  // 响应式数据
  const showCartDialog = ref(false);
  const currentPage = ref(1);
  const pageSize = ref(10);
  const activeName = ref('first');
  const selectedRows = ref([]);

  // 组名管理
  const groupNames = reactive({
    first: 'GroupA',
    second: 'GroupB',
  });

  // 编辑状态管理
  const editingTab = reactive({
    first: false,
    second: false,
  });

  const tempGroupName = ref('');
  const tabInput = ref(null);

  // 分组数据
  const groupAData = reactive([
    {
      runId: 'SRR25728811',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'Pacific Ocean',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      runId: 'SRR25728812',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'Pacific Ocean',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      runId: 'SRR25728814',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'Pacific Ocean',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      runId: 'OER00094854',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'Pacific Ocean',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      runId: 'SRR28427058',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'Pacific Ocean',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      runId: 'SRR16472921',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'Pacific Ocean',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      runId: 'SRR23495137',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'Pacific Ocean',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      runId: 'SRR25728903',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'Pacific Ocean',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      runId: 'SRR25728899',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'Pacific Ocean',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
    {
      runId: 'SRR7663137',
      sourceType: 'AntNest',
      projectId: 'PRJNA1000040',
      latitude: '124.1256',
      longitude: '124.1256',
      hydrosphereType: 'Marine',
      geolocation: 'Pacific Ocean',
      waterBodyType: 'Bay',
      depth: '3000m',
      temperature: '2°C',
      salinity: '40‰',
      ph: 10,
      criticalZone: 'Yes',
      samplingSubstrate: 'Sediment',
      country: 'USA',
      waterBodyName: 'Gulf of Mexico',
    },
  ]);

  // GroupB 数据（空数组作为示例）
  const groupBData = reactive([]);

  // 计算属性：分页数据
  const paginatedData = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    return groupAData.slice(start, end);
  });

  // 表格引用
  const table = ref(null);

  // 表格选择处理
  const handleSelectionChange = selection => {
    selectedRows.value = selection;
    console.log('Selection changed:', selection);
  };

  // 组名编辑相关方法
  const startEditTab = tabName => {
    editingTab[tabName] = true;
    tempGroupName.value = groupNames[tabName];
    nextTick(() => {
      if (tabInput.value) {
        tabInput.value.focus();
        tabInput.value.select();
      }
    });
  };

  const finishEditTab = tabName => {
    if (tempGroupName.value.trim()) {
      groupNames[tabName] = tempGroupName.value.trim();
      ElMessage.success('Group name updated successfully');
    } else {
      ElMessage.warning('Group name cannot be empty');
    }
    // 无论是否成功，都退出编辑模式
    editingTab[tabName] = false;
    tempGroupName.value = '';
  };

  // 删除组相关方法
  const confirmDeleteGroup = async tabName => {
    const groupName = groupNames[tabName];
    const dataCount =
      tabName === 'first' ? groupAData.length : groupBData.length;

    try {
      await ElMessageBox.confirm(
        `Are you sure you want to delete group "${groupName}"? This will permanently delete ${dataCount} item(s).`,
        'Confirm Delete Group',
        {
          confirmButtonText: 'Delete',
          cancelButtonText: 'Cancel',
          type: 'warning',
          dangerouslyUseHTMLString: true,
        },
      );
    } catch {
      // 用户取消删除
    }
  };

  // Tab编辑处理
  const handleTabsEdit = (targetName, action) => {
    if (action === 'remove') {
      confirmDeleteGroup(targetName);
    }
  };

  // 确认选择
  const confirmSelection = () => {
    if (selectedRows.value.length === 0) {
      ElMessage.warning('Please select items first');
      return;
    }

    emit('confirm-selection', {
      selectedData: selectedRows.value,
      count: selectedRows.value.length,
    });

    showCartDialog.value = false;

    ElMessage.success(
      `${selectedRows.value.length} items selected successfully`,
    );
  };
</script>

<style lang="scss" scoped>
  // 弹框头部样式
  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .dialog-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .export-all-btn {
      background: #1e7cb2;
      border-color: #1e7cb2;

      &:hover {
        background: #1a6ba0;
        border-color: #1a6ba0;
      }
    }
  }

  // 表格底部操作区域
  .table-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 16px;
    padding: 0 8px;

    .action-buttons {
      display: flex;
      gap: 12px;

      .el-button {
        font-weight: 500;
      }
    }

    .pagination {
      margin: 0;
    }
  }

  // 空分组样式
  .empty-group {
    padding: 40px 0;
    text-align: center;
  }

  // Tab标签样式
  .tab-label {
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;

    span {
      cursor: pointer;
      user-select: none;
    }

    .tab-input {
      width: 120px;
    }

    .delete-tab-btn {
      width: 20px;
      height: 20px;
      padding: 0;
      margin-left: 4px;
      opacity: 0.7;
      transition: opacity 0.2s ease;

      &:hover {
        opacity: 1;
        background: #f56c6c;
        border-color: #f56c6c;
      }

      :deep(.el-icon) {
        font-size: 12px;
      }
    }
  }

  // 添加组按钮样式
  .add-group-btn {
    width: 24px;
    height: 24px;
    padding: 0;
    background: #1e7cb2;
    border-color: #1e7cb2;

    &:hover {
      background: #1a6ba0;
      border-color: #1a6ba0;
    }

    :deep(.el-icon) {
      font-size: 14px;
    }
  }

  // Tab容器样式优化
  :deep(.el-tabs__header) {
    margin-bottom: 16px;
  }

  :deep(.el-tabs__item) {
    padding: 0 8px !important;

    &:hover .tab-label .delete-tab-btn {
      opacity: 1;
    }
  }

  :deep(.el-tabs__nav-wrap::after) {
    display: none;
  }

  .floating-cart-icon {
    position: fixed;
    top: 80%;
    right: 20px;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    background: #1e7cb2;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(30, 124, 178, 0.3);
    transition: all 0.3s ease;
    z-index: 1000;

    &:hover {
      background: #1a6ba0;
      transform: translateY(-50%) scale(1.1);
      box-shadow: 0 6px 16px rgba(30, 124, 178, 0.4);
    }

    .cart-icon {
      font-size: 24px;
      color: white;
    }
  }

  .cart-badge {
    position: absolute;
    top: -5px;
    right: -5px;
  }

  // inline variant styles (from CartIcon component)
  .cart-icon-container {
    .cart-icon-small {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 4px;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      position: relative;

      &:hover {
        background: #e8f4fd;
        border-color: #1e7cb2;
      }

      .icon {
        font-size: 26px;
        color: #1e7cb2;
      }

      .badge {
        position: absolute;
        top: -3px;
        left: 20px;
      }

      .cart-text {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-left: 8px;
      }
    }
  }

  .cart-content {
    .group-list {
      .group-item {
        padding: 16px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        margin-bottom: 12px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: #f8f9fa;
          border-color: #1e7cb2;
        }

        &:last-child {
          margin-bottom: 0;
        }

        .group-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .group-name {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #1e7cb2;
          }

          .group-count {
            font-size: 12px;
            color: #666;
            background: #f0f0f0;
            padding: 2px 8px;
            border-radius: 10px;
          }
        }

        .group-preview {
          font-size: 13px;
          color: #666;

          .preview-item {
            color: #1e7cb2;
            font-weight: 500;
          }

          .more-items {
            color: #999;
            font-style: italic;
          }
        }
      }
    }

    .empty-state {
      text-align: center;
      padding: 40px 20px;
      color: #999;

      .empty-icon {
        font-size: 48px;
        color: #ddd;
        margin-bottom: 16px;
      }

      p {
        margin: 0;
        font-size: 16px;
      }
    }
  }

  .group-detail {
    .el-table {
      font-size: 13px;
    }
  }
  .dlete-all {
    position: absolute;
    right: 40px;
    top: 32px;
  }
  .el-icon {
    flex-shrink: 0 !important;
  }
</style>
